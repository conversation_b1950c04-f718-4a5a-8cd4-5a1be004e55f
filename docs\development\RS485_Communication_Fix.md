# RS485通信问题修复文档

## 问题描述
用户报告RS485通信无法正常接收和发送数据。

## 问题分析

### 1. DMA配置问题
**问题**：在`bsp_usart_init()`函数中，DMA接收缓冲区大小配置为256字节，但实际使用的rxbuffer大小为512字节。

**位置**：`Components/bsp/mcu_cmic_gd32f470vet6.c:66`

**修复前**：
```c
dma_init_struct.number = 256;
```

**修复后**：
```c
dma_init_struct.number = 512;  // 修复：与rxbuffer大小一致
```

### 2. 中断处理中的数据长度计算问题
**问题**：在USART1中断处理函数中，接收数据长度计算不正确。

**位置**：`USER/src/gd32f4xx_it.c:162`

**修复前**：
```c
memcpy(uart_dma_buffer, rxbuffer, dma_transfer_number_get(DMA0, DMA_CH5));
```

**修复后**：
```c
uint32_t received_length = 512 - dma_transfer_number_get(DMA0, DMA_CH5);
if(received_length > 0) {
    memcpy(uart_dma_buffer, rxbuffer, received_length);
    rx_flag = 1;
}
```

### 3. RS485方向控制时序优化
**问题**：发送完成后切换到接收模式的时序可能不够稳定。

**位置**：`sysFunction/usart_app.c:54-58`

**修复前**：
```c
while(RESET == usart_flag_get(usart_periph, USART_FLAG_TC));
RS485_RX_ENABLE();
```

**修复后**：
```c
while(RESET == usart_flag_get(usart_periph, USART_FLAG_TC));
delay_1ms(2);  // 增加延时确保数据完全发送
RS485_RX_ENABLE();
```

## 新增功能

### RS485通信测试命令
添加了`rs485 test`命令，用于测试RS485通信功能。

**使用方法**：
1. 通过串口发送命令：`rs485 test`
2. 系统会发送测试数据并显示结果

**实现位置**：
- 函数实现：`sysFunction/usart_app.c:247-255`
- 函数声明：`sysFunction/usart_app.h:29`
- 命令处理：`sysFunction/usart_app.c:279-281`

## 硬件连接检查

### RS485引脚配置
- **TX引脚**：PA2 (USART1_TX)
- **RX引脚**：PA3 (USART1_RX)  
- **方向控制引脚**：PA1 (RS485_CS_PIN)

### 方向控制逻辑
- **发送模式**：PA1 = HIGH (`RS485_TX_ENABLE()`)
- **接收模式**：PA1 = LOW (`RS485_RX_ENABLE()`)

## 调试步骤

### 1. 基本功能测试
```
发送命令: rs485 test
预期输出: 
RS485 Test - Sending data...
Hello RS485!
RS485 Test completed
```

### 2. 硬件连接检查
- 确认RS485收发器芯片连接正确
- 检查A、B差分信号线连接
- 验证终端电阻配置（120Ω）
- 确认电源供电正常

### 3. 波特率和通信参数
- 波特率：115200
- 数据位：8
- 停止位：1
- 校验位：无

### 4. 示波器调试
- 监测PA1方向控制信号
- 检查A、B差分信号质量
- 验证发送和接收时序

## 常见问题排查

### 1. 无法发送数据
- 检查RS485方向控制引脚是否正常工作
- 确认发送缓冲区数据是否正确
- 验证USART发送使能配置

### 2. 无法接收数据
- 检查DMA配置是否正确
- 确认IDLE中断是否正常触发
- 验证接收缓冲区是否被正确处理

### 3. 数据丢失或错误
- 检查波特率设置是否匹配
- 确认信号完整性（使用示波器）
- 验证终端电阻配置

## 测试验证

### 1. 自环测试
将RS485的A、B线短接，发送数据应该能够接收到相同数据。

### 2. 双设备通信测试
使用两个设备进行通信测试，验证双向数据传输。

### 3. 长距离通信测试
在实际应用距离下测试通信稳定性。

## 修复总结

1. **修复了DMA缓冲区大小不匹配问题**
2. **优化了中断处理中的数据长度计算**
3. **改进了RS485方向控制时序**
4. **添加了RS485通信测试功能**
5. **提供了完整的调试和排查指南**

这些修复应该能够解决RS485通信无法正常收发数据的问题。如果问题仍然存在，建议按照调试步骤进行硬件和软件的进一步排查。
