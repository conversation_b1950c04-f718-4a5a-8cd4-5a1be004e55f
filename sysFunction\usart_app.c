#include "mcu_cmic_gd32f470vet6.h"
#include "gd25qxx.h"
#include "diskio.h"
#include "sdio_sdcard.h"
#include "gd32f4xx_rtc.h"
#include "gd32f4xx_usart.h"
#include "ff.h"
#include "sd_app.h"

__IO uint16_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[512] = {0};

// Configuration parameters global variable
static config_params_t g_config_params = {
    1.0f,                           // ratio
    1.0f,                           // limit
    5,                              // sample_cycle
    OUTPUT_FORMAT_NORMAL,           // output_format
    false,                          // encrypt_mode_enabled
    0,                              // log_id
    false,                          // log_id_user_set
    "2025-CIMC-2025925162",        // device_id
    0,                              // magic
    0                               // checksum
};

int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;
    // 初始化可变参数列表
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    
    // 如果是USART1，需要控制RS485方向
    if(usart_periph == USART1) {
        // 切换到发送模式
        RS485_TX_ENABLE();
        // 等待一小段时间确保切换完成
        delay_1ms(1);
    }
    
    for(tx_count = 0; tx_count < len; tx_count++){
        usart_data_transmit(usart_periph, buffer[tx_count]);
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
    }
    
    // 如果是USART1，发送完成后切换回接收模式
    if(usart_periph == USART1) {
        // 等待发送完全完成
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TC));
        // 切换到接收模式
        RS485_RX_ENABLE();
    }
    
    return len;
}

/*!
    \brief      convert decimal to BCD format
    \param[in]  dec: decimal value (0-99)
    \param[out] none
    \retval     BCD format value
*/
uint8_t dec_to_bcd(uint8_t dec)
{
    return ((dec / 10) << 4) + (dec % 10);
}

/*!
    \brief      convert BCD to decimal format
    \param[in]  bcd: BCD format value
    \param[out] none
    \retval     decimal value
*/
static uint8_t bcd_to_dec(uint8_t bcd)
{
    return ((bcd >> 4) * 10) + (bcd & 0x0F);
}

/*!
    \brief      system self-test function
    \param[in]  none
    \param[out] none
    \retval     none
*/
void system_selftest(void)
{
    uint32_t flash_id;
    DSTATUS sd_status;
    rtc_parameter_struct rtc_time;
    uint32_t sd_capacity;
    bool flash_ok = false;
    bool tf_card_ok = false;

    my_printf(DEBUG_USART, "======system selftest======\r\n");

    // Flash检测
    flash_id = spi_flash_read_id();
    if(flash_id != 0 && flash_id != 0xFFFFFF) {
        my_printf(DEBUG_USART, "flash........ok\r\n");
        flash_ok = true;
    } else {
        my_printf(DEBUG_USART, "flash........error\r\n");
        flash_ok = false;
    }

    // SD卡检测
    sd_status = disk_initialize(0);
    if(sd_status == 0) { // 0表示成功，对应RES_OK
        my_printf(DEBUG_USART, "TF card......ok\r\n");
        tf_card_ok = true;
    } else {
        my_printf(DEBUG_USART, "TF card......error\r\n");
        tf_card_ok = false;
    }

    // 输出详细信息
    my_printf(DEBUG_USART, "flash ID:0x%06lX\r\n", flash_id);
    if(sd_status == 0) {
        sd_capacity = sd_card_capacity_get();
        my_printf(DEBUG_USART, "TF card memory:%lu KB\r\n", sd_capacity);
    } else {
        my_printf(DEBUG_USART, "can not find TF card\r\n");
    }

    // RTC时间显示
    rtc_current_time_get(&rtc_time);
    my_printf(DEBUG_USART, "RTC.20%02d-%02d-%02d %02d:%02d:%02d\r\n",
              bcd_to_dec(rtc_time.year), rtc_time.month, bcd_to_dec(rtc_time.date),
              bcd_to_dec(rtc_time.hour), bcd_to_dec(rtc_time.minute), bcd_to_dec(rtc_time.second));

    my_printf(DEBUG_USART, "======system selftest======\r\n");

    // 记录测试结果日志
    if(flash_ok && tf_card_ok) {
        storage_log_operation("test ok");
    } else if(!tf_card_ok) {
        storage_log_operation("test error: tf card not found");
    } else {
        storage_log_operation("test error: flash not found");
    }
}

/*!
    \brief      convert decimal month to RTC month enum
    \param[in]  month: decimal month value (1-12)
    \param[out] none
    \retval     RTC month enum value
*/
static uint8_t month_to_rtc_enum(uint8_t month)
{
    const uint8_t month_table[] = {
        0, RTC_JAN, RTC_FEB, RTC_MAR, RTC_APR, RTC_MAY, RTC_JUN,
        RTC_JUL, RTC_AUG, RTC_SEP, RTC_OCT, RTC_NOV, RTC_DEC
    };

    if(month >= 1 && month <= 12) {
        return month_table[month];
    }
    return RTC_JAN; // 默认返回1月
}

/*!
    \brief      RTC time configuration handler
    \param[in]  time_str: time string to parse
    \param[out] none
    \retval     none
*/
void rtc_config_handler(char *time_str)
{
    int year, month, day, hour, minute, second;
    rtc_parameter_struct rtc_time;
    extern rtc_parameter_struct rtc_initpara;
    extern uint32_t prescaler_a, prescaler_s;

    // 解析时间格式
    if(sscanf(time_str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second) == 6 ||
       sscanf(time_str, "%d %d %d %d %d %d", &year, &month, &day, &hour, &minute, &second) == 6) {

        // 验证时间数值的有效性
        if(year < 2000 || year > 2099 || month < 1 || month > 12 ||
           day < 1 || day > 31 || hour < 0 || hour > 23 ||
           minute < 0 || minute > 59 || second < 0 || second > 59) {
            my_printf(DEBUG_USART, "Invalid time range\r\n");
            return;
        }

        // 设置RTC参数
        rtc_time.year = dec_to_bcd(year % 100);
        rtc_time.month = month_to_rtc_enum(month);
        rtc_time.date = dec_to_bcd(day);
        rtc_time.hour = dec_to_bcd(hour);
        rtc_time.minute = dec_to_bcd(minute);
        rtc_time.second = dec_to_bcd(second);
        rtc_time.day_of_week = RTC_MONDAY; // 简化处理，固定为周一
        rtc_time.display_format = RTC_24HOUR;
        rtc_time.am_pm = RTC_AM;
        rtc_time.factor_asyn = prescaler_a;
        rtc_time.factor_syn = prescaler_s;

        // 更新RTC
        if(rtc_init(&rtc_time) == SUCCESS) {
            my_printf(DEBUG_USART, "RTC Config success\r\n");
            my_printf(DEBUG_USART, "Time:%04d-%02d-%02d %02d:%02d:%02d\r\n",
                      year, month, day, hour, minute, second);
            // Log RTC configuration
            char log_msg[64];
            sprintf(log_msg, "rtc config success to %04d-%02d-%02d %02d:%02d:%02d",
                    year, month, day, hour, minute, second);
            storage_log_operation(log_msg);
        } else {
            my_printf(DEBUG_USART, "RTC Config failed\r\n");
            storage_log_operation("rtc config failed");
        }
    } else {
        my_printf(DEBUG_USART, "Invalid time format\r\n");
        my_printf(DEBUG_USART, "Please use format: YYYY-MM-DD HH:MM:SS\r\n");
    }
}

/*!
    \brief      show current RTC time
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_show_current_time(void)
{
    rtc_parameter_struct rtc_time;
    extern rtc_parameter_struct rtc_initpara;

    // 获取当前时间
    rtc_current_time_get(&rtc_time);

    // 输出当前时间，格式与RTC Config成功后的格式一致
    my_printf(DEBUG_USART, "Current Time:20%02d-%02d-%02d %02d:%02d:%02d\r\n",
              bcd_to_dec(rtc_time.year), rtc_time.month, bcd_to_dec(rtc_time.date),
              bcd_to_dec(rtc_time.hour), bcd_to_dec(rtc_time.minute), bcd_to_dec(rtc_time.second));
}

void uart_task(void)
{
    static cmd_state_t cmd_state = CMD_STATE_IDLE;
    char *cmd;
    char *newline_pos;

    if(!rx_flag) return;

    // 获取命令字符串并移除换行符
    cmd = (char*)uart_dma_buffer;
    newline_pos = strpbrk(cmd, "\r\n");
    if(newline_pos) {
        *newline_pos = '\0';
    }

    // 命令解析状态机
    switch(cmd_state) {
        case CMD_STATE_IDLE:
            if(strcmp(cmd, "test") == 0) {
                storage_log_operation("system hardware test");
                system_selftest();
            } else if(strcmp(cmd, "RTC Config") == 0) {
                my_printf(DEBUG_USART, "Input Datetime:\r\n");
                storage_log_operation("rtc config");
                cmd_state = CMD_STATE_RTC_CONFIG;
            } else if(strcmp(cmd, "RTC now") == 0) {
                rtc_show_current_time();
            } else if(strcmp(cmd, "conf") == 0) {
                config_read_handler();
            } else if(strcmp(cmd, "ratio") == 0) {
                ratio_config_handler();
                storage_log_operation("ratio config");
                cmd_state = CMD_STATE_RATIO_CONFIG;
            } else if(strcmp(cmd, "limit") == 0) {
                limit_config_handler();
                storage_log_operation("limit config");
                cmd_state = CMD_STATE_LIMIT_CONFIG;
            } else if(strcmp(cmd, "config save") == 0) {
                config_save_handler();
                storage_log_operation("Configuration saved to Flash");
            } else if(strcmp(cmd, "config read") == 0) {
                config_read_flash_handler();
                storage_log_operation("Configuration loaded from Flash");
            } else if(strcmp(cmd, "start") == 0) {
                sampling_start_handler();
                // 记录串口命令启动采样日志，包含周期信息
                char log_msg[64];
                sprintf(log_msg, "sample start - cycle %ds (command)", get_sampling_cycle());
                storage_log_operation(log_msg);
            } else if(strcmp(cmd, "stop") == 0) {
                sampling_stop_handler();
                storage_log_operation("sample stop (command)");
            } else if(strcmp(cmd, "hide") == 0) {
                set_output_format(OUTPUT_FORMAT_HIDDEN);
                storage_log_operation("hide data");
            } else if(strcmp(cmd, "unhide") == 0) {
                set_output_format(OUTPUT_FORMAT_NORMAL);
                storage_log_operation("unhide data");
            } else if(strcmp(cmd, "device id") == 0) {
                my_printf(DEBUG_USART, "Current Device ID: %s\r\n", get_device_id());
            } else if(strncmp(cmd, "set device id ", 14) == 0) {
                char* new_id = cmd + 14; // 跳过"set device id "
                if(strlen(new_id) > 0 && strlen(new_id) < 32) {
                    if(set_device_id(new_id)) {
                        my_printf(DEBUG_USART, "Device ID set to: %s\r\n", new_id);
                        storage_log_operation("Device ID updated");
                    } else {
                        my_printf(DEBUG_USART, "Failed to set device ID\r\n");
                    }
                } else {
                    my_printf(DEBUG_USART, "Invalid device ID length (max 31 characters)\r\n");
                }
            } else if(strcmp(cmd, "log id") == 0) {
                my_printf(DEBUG_USART, "Current power-on count (log ID): %lu\r\n", get_log_id());
            } else if(strncmp(cmd, "set log id ", 11) == 0) {
                char* id_str = cmd + 11; // 跳过"set log id "
                uint32_t new_log_id = atol(id_str);
                if(set_log_id(new_log_id)) {
                    my_printf(DEBUG_USART, "Power-on count (log ID) set to: %lu\r\n", new_log_id);
                    my_printf(DEBUG_USART, "Next log file will be: log%lu.txt\r\n", new_log_id);
                    storage_log_operation("Power-on count manually set");
                } else {
                    my_printf(DEBUG_USART, "Failed to set power-on count\r\n");
                }
            } else if(strlen(cmd) > 0) {
                my_printf(DEBUG_USART, "Unknown command: %s\r\n", cmd);
                my_printf(DEBUG_USART, "Available commands: test, RTC Config, RTC now, conf, ratio, limit, config save, config read, start, stop, hide, unhide, device id, set device id <ID>, log id, set log id <ID>\r\n");
            }
            break;

        case CMD_STATE_RTC_CONFIG:
            rtc_config_handler(cmd);
            cmd_state = CMD_STATE_IDLE;
            break;

        case CMD_STATE_RATIO_CONFIG:
            ratio_input_handler(cmd);
            cmd_state = CMD_STATE_IDLE;
            break;

        case CMD_STATE_LIMIT_CONFIG:
            limit_input_handler(cmd);
            cmd_state = CMD_STATE_IDLE;
            break;

        default:
            cmd_state = CMD_STATE_IDLE;
            break;
    }

    // 清理接收缓冲区并重置标志
    memset(uart_dma_buffer, 0, 512);
    rx_flag = 0;
}

/*!
    \brief      get pointer to configuration parameters
    \param[in]  none
    \param[out] none
    \retval     pointer to configuration parameters structure
*/
config_params_t* get_config_params(void)
{
    return &g_config_params;
}

/*!
    \brief      get current output format
    \param[in]  none
    \param[out] none
    \retval     current output format
*/
output_format_t get_output_format(void)
{
    return (output_format_t)g_config_params.output_format;
}

/*!
    \brief      set output format and save to flash
    \param[in]  format: output format to set
    \param[out] none
    \retval     none
*/
void set_output_format(output_format_t format)
{
    if(validate_output_format((uint8_t)format)) {
        g_config_params.output_format = (uint8_t)format;
        config_save_to_flash();
    }
}

/*!
    \brief      get current encrypt mode
    \param[in]  none
    \param[out] none
    \retval     current encrypt mode
*/
bool get_encrypt_mode(void)
{
    return g_config_params.encrypt_mode_enabled;
}

/*!
    \brief      set encrypt mode and save to flash
    \param[in]  enabled: encrypt mode to set
    \param[out] none
    \retval     none
*/
void set_encrypt_mode(bool enabled)
{
    if(validate_encrypt_mode(enabled)) {
        g_config_params.encrypt_mode_enabled = enabled;
        config_save_to_flash();
    }
}

/*!
    \brief      get current log ID
    \param[in]  none
    \param[out] none
    \retval     current log ID
*/
uint32_t get_log_id(void)
{
    return g_config_params.log_id;
}

/*!
    \brief      increment log ID and save to flash
    \param[in]  none
    \param[out] none
    \retval     none
*/
void increment_log_id(void)
{
    // 如果log_id为0且是用户手动设置的，则跳过第一次自增，让用户从log0.txt开始记录
    if(g_config_params.log_id == 0 && g_config_params.log_id_user_set) {
        // 清除用户设置标志，下次上电时正常自增
        g_config_params.log_id_user_set = false;
        config_save_to_flash();
        return;
    }

    uint32_t new_id = g_config_params.log_id + 1;
    if(validate_log_id(new_id)) {
        g_config_params.log_id = new_id;
        config_save_to_flash();
    }
}

/*!
    \brief      set log ID manually and save to flash
    \param[in]  new_log_id: new log ID value to set
    \param[out] none
    \retval     true if successful, false if invalid
*/
bool set_log_id(uint32_t new_log_id)
{
    if(validate_log_id(new_log_id)) {
        g_config_params.log_id = new_log_id;
        // 如果用户设置为0，则标记为用户手动设置，下次上电时跳过第一次自增
        g_config_params.log_id_user_set = (new_log_id == 0);
        config_save_to_flash();
        return true;
    }
    return false;
}





/*!
    \brief      get device ID string
    \param[in]  none
    \param[out] none
    \retval     pointer to device ID string
*/
const char* get_device_id(void)
{
    return g_config_params.device_id;
}

/*!
    \brief      set device ID string and save to flash
    \param[in]  device_id: device ID string to set (max 31 characters)
    \param[out] none
    \retval     true if successful, false if invalid
*/
bool set_device_id(const char* device_id)
{
    if(device_id == NULL || strlen(device_id) >= sizeof(g_config_params.device_id)) {
        return false;
    }

    // Copy device ID string
    strncpy(g_config_params.device_id, device_id, sizeof(g_config_params.device_id) - 1);
    g_config_params.device_id[sizeof(g_config_params.device_id) - 1] = '\0'; // 确保字符串结束

    // Save to flash
    config_save_to_flash();
    return true;
}

/*!
    \brief      check if config.ini file exists in file system
    \param[in]  none
    \param[out] none
    \retval     true if file exists, false otherwise
*/
bool config_ini_file_exists(void)
{
    FIL test_file;
    FRESULT result;

    result = f_open(&test_file, "0:/config.ini", FA_READ);
    if(result == FR_OK) {
        f_close(&test_file);
        return true;
    }

    return false;
}

/*!
    \brief      create config.ini file in file system with current configuration
    \param[in]  none
    \param[out] none
    \retval     true if successful, false otherwise
*/
bool create_config_ini_file(void)
{
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    char buffer[256];
    config_params_t *config = get_config_params();

    // Open file for writing (create new or overwrite existing)
    result = f_open(&config_file, "0:/config.ini", FA_WRITE | FA_CREATE_ALWAYS);
    if(result != FR_OK) {
        return false;
    }

    // Write Ratio section
    sprintf(buffer, "[Ratio]\r\n");
    result = f_write(&config_file, buffer, strlen(buffer), &bytes_written);
    if(result != FR_OK) {
        f_close(&config_file);
        return false;
    }

    // Write ratio value with Ch0 key (with spaces around =)
    sprintf(buffer, "Ch0 = %.2f\r\n", config->ratio);
    result = f_write(&config_file, buffer, strlen(buffer), &bytes_written);
    if(result != FR_OK) {
        f_close(&config_file);
        return false;
    }

    // Write empty line
    sprintf(buffer, "\r\n");
    result = f_write(&config_file, buffer, strlen(buffer), &bytes_written);
    if(result != FR_OK) {
        f_close(&config_file);
        return false;
    }

    // Write Limit section
    sprintf(buffer, "[Limit]\r\n");
    result = f_write(&config_file, buffer, strlen(buffer), &bytes_written);
    if(result != FR_OK) {
        f_close(&config_file);
        return false;
    }

    // Write limit value with Ch0 key (with spaces around =)
    sprintf(buffer, "Ch0 = %.2f\r\n", config->limit);
    result = f_write(&config_file, buffer, strlen(buffer), &bytes_written);
    if(result != FR_OK) {
        f_close(&config_file);
        return false;
    }

    // Sync and close file
    f_sync(&config_file);
    f_close(&config_file);

    return true;
}

/*!
    \brief      validate ratio parameter range
    \param[in]  value: ratio value to validate
    \param[out] none
    \retval     true if valid (0-100), false otherwise
*/
bool validate_ratio(float value)
{
    return (value >= 0.0f && value <= 100.0f);
}

/*!
    \brief      validate limit parameter range
    \param[in]  value: limit value to validate
    \param[out] none
    \retval     true if valid (0-200), false otherwise
*/
bool validate_limit(float value)
{
    return (value >= 0.0f && value <= 200.0f);
}

/*!
    \brief      validate sample cycle parameter
    \param[in]  value: sample cycle value to validate
    \param[out] none
    \retval     true if valid (5/10/15), false otherwise
*/
bool validate_sample_cycle(uint8_t value)
{
    return (value == 5 || value == 10 || value == 15);
}

/*!
    \brief      validate output format parameter
    \param[in]  value: output format value to validate
    \param[out] none
    \retval     true if valid (0/1), false otherwise
*/
bool validate_output_format(uint8_t value)
{
    return (value == OUTPUT_FORMAT_NORMAL || value == OUTPUT_FORMAT_HIDDEN);
}

/*!
    \brief      validate encrypt mode parameter
    \param[in]  value: encrypt mode value to validate
    \param[out] none
    \retval     true if valid (true/false), false otherwise
*/
bool validate_encrypt_mode(bool value)
{
    return true; // bool类型总是有效的
}

/*!
    \brief      validate log ID parameter
    \param[in]  value: log ID value to validate
    \param[out] none
    \retval     true if valid (0-999999), false otherwise
*/
bool validate_log_id(uint32_t value)
{
    return (value <= 999999); // 限制日志ID在合理范围内
}

/*!
    \brief      parse float input from string
    \param[in]  input: input string to parse
    \param[out] none
    \retval     parsed float value, or -1.0f if parsing failed
*/
float parse_float_input(char *input)
{
    char *endptr;
    float value;

    if(input == NULL || strlen(input) == 0) {
        return -1.0f;
    }

    value = strtof(input, &endptr);

    // 检查是否完全解析（没有剩余字符）
    if(*endptr == '\0' || *endptr == '\r' || *endptr == '\n') {
        return value;
    }

    return -1.0f; // 解析失败
}

/*!
    \brief      calculate simple checksum for config parameters
    \param[in]  params: pointer to config parameters structure
    \param[out] none
    \retval     calculated checksum value
*/
uint32_t calculate_checksum(config_params_t *params)
{
    uint32_t checksum = 0;
    uint8_t *data = (uint8_t*)params;
    uint32_t size = sizeof(config_params_t) - sizeof(uint32_t); // 不包括checksum字段本身

    for(uint32_t i = 0; i < size; i++) {
        checksum += data[i];
    }

    return checksum;
}

/*!
    \brief      save current configuration parameters to Flash
    \param[in]  none
    \param[out] none
    \retval     true if save successful, false otherwise
*/
bool config_save_to_flash(void)
{
    config_params_t temp_params = g_config_params;

    // 设置magic和checksum字段
    temp_params.magic = CONFIG_MAGIC;
    temp_params.checksum = calculate_checksum(&temp_params);

    // 擦除Flash扇区
    spi_flash_sector_erase(CONFIG_FLASH_ADDR);

    // 写入数据到Flash
    spi_flash_buffer_write((uint8_t*)&temp_params, CONFIG_FLASH_ADDR, sizeof(config_params_t));

    return true;
}

/*!
    \brief      load configuration parameters from Flash
    \param[in]  none
    \param[out] none
    \retval     true if load successful, false if using default values
*/
bool config_load_from_flash(void)
{
    config_params_t temp_params;
    uint32_t calculated_checksum;

    // 从Flash读取数据
    spi_flash_buffer_read((uint8_t*)&temp_params, CONFIG_FLASH_ADDR, sizeof(config_params_t));

    // 验证magic字段
    if(temp_params.magic != CONFIG_MAGIC) {
        // Magic不匹配，使用默认值
        g_config_params.ratio = 1.0f;
        g_config_params.limit = 1.0f;
        g_config_params.sample_cycle = 5;
        g_config_params.output_format = OUTPUT_FORMAT_NORMAL;
        g_config_params.encrypt_mode_enabled = false;
        g_config_params.log_id = 0;
        g_config_params.log_id_user_set = false;
        strcpy(g_config_params.device_id, "2025-CIMC-2025925162");
        g_config_params.magic = 0;
        g_config_params.checksum = 0;
        return false;
    }

    // 验证checksum
    calculated_checksum = calculate_checksum(&temp_params);
    if(temp_params.checksum != calculated_checksum) {
        // 校验和不匹配，使用默认值
        g_config_params.ratio = 1.0f;
        g_config_params.limit = 1.0f;
        g_config_params.sample_cycle = 5;
        g_config_params.output_format = OUTPUT_FORMAT_NORMAL;
        g_config_params.encrypt_mode_enabled = false;
        g_config_params.log_id = 0;
        g_config_params.log_id_user_set = false;
        strcpy(g_config_params.device_id, "2025-CIMC-2025925162");
        g_config_params.magic = 0;
        g_config_params.checksum = 0;
        return false;
    }

    // 验证参数范围
    if(!validate_ratio(temp_params.ratio) || !validate_limit(temp_params.limit) ||
       !validate_sample_cycle(temp_params.sample_cycle) || !validate_output_format(temp_params.output_format) ||
       !validate_encrypt_mode(temp_params.encrypt_mode_enabled) || !validate_log_id(temp_params.log_id)) {
        // 参数范围无效，使用默认值
        g_config_params.ratio = 1.0f;
        g_config_params.limit = 1.0f;
        g_config_params.sample_cycle = 5;
        g_config_params.output_format = OUTPUT_FORMAT_NORMAL;
        g_config_params.encrypt_mode_enabled = false;
        g_config_params.log_id = 0;
        g_config_params.log_id_user_set = false;
        strcpy(g_config_params.device_id, "2025-CIMC-2025925162");
        g_config_params.magic = 0;
        g_config_params.checksum = 0;
        return false;
    }

    // 所有验证通过，更新全局参数
    g_config_params = temp_params;
    return true;
}

// INI parsing state enumeration
typedef enum {
    INI_PARSE_IDLE = 0,
    INI_PARSE_RATIO_SECTION,
    INI_PARSE_LIMIT_SECTION
} ini_parse_state_t;

/*!
    \brief      trim whitespace characters from string
    \param[in]  str: string to trim
    \param[out] none
    \retval     pointer to trimmed string
*/
char* trim_whitespace(char *str)
{
    char *end;

    if(str == NULL) return NULL;

    // 去除前导空白字符
    while(*str == ' ' || *str == '\t' || *str == '\r' || *str == '\n') {
        str++;
    }

    if(*str == '\0') return str; // 全是空白字符

    // 去除尾部空白字符
    end = str + strlen(str) - 1;
    while(end > str && (*end == ' ' || *end == '\t' || *end == '\r' || *end == '\n')) {
        end--;
    }

    // 添加字符串结束符
    *(end + 1) = '\0';

    return str;
}

/*!
    \brief      parse INI configuration file content
    \param[in]  content: INI file content string
    \param[in]  params: pointer to config parameters structure to update
    \param[out] none
    \retval     true if parsing successful, false otherwise
*/
bool parse_config_ini(char *content, config_params_t *params)
{
    char *line;
    char *line_end;
    char *key, *value;
    char *equal_pos;
    ini_parse_state_t parse_state = INI_PARSE_IDLE;
    float temp_ratio = params->ratio;  // 保存原值，解析失败时恢复
    float temp_limit = params->limit;
    bool ratio_found = false, limit_found = false;

    if(content == NULL || params == NULL) {
        return false;
    }

    // 逐行解析
    line = content;
    while(line != NULL && *line != '\0') {
        // 找到行结束位置
        line_end = strpbrk(line, "\r\n");
        if(line_end != NULL) {
            *line_end = '\0'; // 临时截断
        }

        // 去除空白字符
        line = trim_whitespace(line);

        // 跳过空行和注释行
        if(strlen(line) == 0 || line[0] == ';' || line[0] == '#') {
            goto next_line;
        }

        // 检查是否是章节标记
        if(line[0] == '[' && line[strlen(line)-1] == ']') {
            if(strncmp(line, "[Ratio]", 7) == 0) {
                parse_state = INI_PARSE_RATIO_SECTION;
            } else if(strncmp(line, "[Limit]", 7) == 0) {
                parse_state = INI_PARSE_LIMIT_SECTION;
            } else {
                parse_state = INI_PARSE_IDLE;
            }
            goto next_line;
        }

        // 解析键值对
        equal_pos = strchr(line, '=');
        if(equal_pos != NULL) {
            *equal_pos = '\0';
            key = trim_whitespace(line);
            value = trim_whitespace(equal_pos + 1);

            // 检查是否是Ch0键
            if(strcmp(key, "Ch0") == 0) {
                float parsed_value = parse_float_input(value);
                if(parsed_value >= 0.0f) { // 解析成功
                    if(parse_state == INI_PARSE_RATIO_SECTION && validate_ratio(parsed_value)) {
                        temp_ratio = parsed_value;
                        ratio_found = true;
                    } else if(parse_state == INI_PARSE_LIMIT_SECTION && validate_limit(parsed_value)) {
                        temp_limit = parsed_value;
                        limit_found = true;
                    }
                }
            }
        }

next_line:
        if(line_end != NULL) {
            *line_end = '\n'; // 恢复原字符
            line = line_end + 1;
            // 跳过可能的\r\n组合
            if(*line == '\n' && *(line-1) == '\r') {
                line++;
            }
        } else {
            break;
        }
    }

    // 只有当两个参数都找到且有效时才更新
    if(ratio_found && limit_found) {
        params->ratio = temp_ratio;
        params->limit = temp_limit;
        return true;
    }

    return false;
}

/*!
    \brief      handle configuration file reading command
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_read_handler(void)
{
    FIL config_file;
    FRESULT fr;
    UINT bytes_read;
    static char file_buffer[2048]; // 使用静态数组替代动态分配
    config_params_t temp_params;

    // 首先更新config.ini文件，确保它包含当前Flash中的最新值
    if(!create_config_ini_file()) {
        my_printf(DEBUG_USART, "Failed to update config.ini file\r\n");
        return;
    }

    // 尝试打开config.ini文件
    fr = f_open(&config_file, "0:/config.ini", FA_READ);
    if(fr != FR_OK) {
        my_printf(DEBUG_USART, "Failed to open config.ini file\r\n");
        return;
    }

    // 读取文件内容
    fr = f_read(&config_file, file_buffer, 2047, &bytes_read);
    f_close(&config_file);

    if(fr != FR_OK) {
        my_printf(DEBUG_USART, "Failed to read config.ini file\r\n");
        return;
    }

    // 添加字符串结束符
    file_buffer[bytes_read] = '\0';

    // 备份当前参数
    temp_params = g_config_params;

    // 解析INI文件内容
    if(parse_config_ini(file_buffer, &temp_params)) {
        // 解析成功，更新全局参数
        g_config_params = temp_params;

        // 保存到Flash
        config_save_to_flash();

        // 输出结果（注意：Limt拼写错误是按照任务要求）
        my_printf(DEBUG_USART, "Ratio=%.1f\r\n", g_config_params.ratio);
        my_printf(DEBUG_USART, "Limt=%.1f\r\n", g_config_params.limit);
        my_printf(DEBUG_USART, "comfig read success\r\n");
    } else {
        my_printf(DEBUG_USART, "Failed to parse config.ini file\r\n");
    }
}

/*!
    \brief      handle ratio configuration command
    \param[in]  none
    \param[out] none
    \retval     none
*/
void ratio_config_handler(void)
{
    my_printf(DEBUG_USART, "Ratio=%.1f\r\n", g_config_params.ratio);
    my_printf(DEBUG_USART, "Input value(0~100):\r\n");
}

/*!
    \brief      handle limit configuration command
    \param[in]  none
    \param[out] none
    \retval     none
*/
void limit_config_handler(void)
{
    my_printf(DEBUG_USART, "limit=%.1f\r\n", g_config_params.limit);
    my_printf(DEBUG_USART, "Input value(0~200):\r\n");
}

/*!
    \brief      handle ratio input processing
    \param[in]  input: user input string
    \param[out] none
    \retval     none
*/
void ratio_input_handler(char *input)
{
    float new_value = parse_float_input(input);

    if(new_value >= 0.0f && validate_ratio(new_value)) {
        g_config_params.ratio = new_value;
        my_printf(DEBUG_USART, "ratio modified success\r\n");
        my_printf(DEBUG_USART, "Ratio=%.1f\r\n", g_config_params.ratio);
        // 记录ratio配置成功日志
        char log_msg[64];
        sprintf(log_msg, "ratio config success to %.2f", new_value);
        storage_log_operation(log_msg);
    } else {
        my_printf(DEBUG_USART, "ratio invaild\r\n");
        my_printf(DEBUG_USART, "Ratio=%.1f\r\n", g_config_params.ratio);
    }
}

/*!
    \brief      handle limit input processing
    \param[in]  input: user input string
    \param[out] none
    \retval     none
*/
void limit_input_handler(char *input)
{
    float new_value = parse_float_input(input);

    if(new_value >= 0.0f && validate_limit(new_value)) {
        g_config_params.limit = new_value;
        my_printf(DEBUG_USART, "limit modified success\r\n");
        my_printf(DEBUG_USART, "limit=%.1f\r\n", g_config_params.limit);
        // 记录limit配置成功日志
        char log_msg[64];
        sprintf(log_msg, "limit config success to %.2f", new_value);
        storage_log_operation(log_msg);
    } else {
        my_printf(DEBUG_USART, "limit invalue\r\n");
        my_printf(DEBUG_USART, "limit=%.1f\r\n", g_config_params.limit);
    }
}

/*!
    \brief      handle config save command
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_save_handler(void)
{
    // 输出当前参数值（注意格式：ratio后面没有空格，limit后面有空格）
    my_printf(DEBUG_USART, "ratio:%.1f\r\n", g_config_params.ratio);
    my_printf(DEBUG_USART, "limit :%.1f\r\n", g_config_params.limit);

    // 保存参数到Flash
    if(config_save_to_flash()) {
        my_printf(DEBUG_USART, "save parameters to flash\r\n");
    } else {
        my_printf(DEBUG_USART, "Failed to save parameters to flash\r\n");
    }
}

/*!
    \brief      handle config read from flash command
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_read_flash_handler(void)
{
    // 从Flash读取参数
    if(config_load_from_flash()) {
        my_printf(DEBUG_USART, "read parameters from flash\r\n");
        my_printf(DEBUG_USART, "ratio:%.1f\r\n", g_config_params.ratio);
        my_printf(DEBUG_USART, "limit :%.1f\r\n", g_config_params.limit);
    } else {
        my_printf(DEBUG_USART, "read parameters from flash\r\n");
        my_printf(DEBUG_USART, "ratio:%.1f\r\n", g_config_params.ratio);
        my_printf(DEBUG_USART, "limit :%.1f\r\n", g_config_params.limit);
        my_printf(DEBUG_USART, "Warning: Using default parameters (Flash data invalid)\r\n");
    }
}
